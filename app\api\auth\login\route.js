import { NextResponse } from 'next/server';
import { setAuthCookies } from 'next-firebase-auth-edge/lib/next/cookies';

export async function POST(request) {
  try {
    const { email, idToken } = await request.json();

    const response = new NextResponse(
      JSON.stringify({ success: true }),
      { status: 200 }
    );

    // Set the authentication cookies
    await setAuthCookies(
      response,
      idToken,
      {
        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
        cookieName: "AuthToken",
        cookieSignatureKeys: [process.env.COOKIE_SIGNATURE_KEY],
        serviceAccount: {
          projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        }
      }
    );

    return response;
  } catch (error) {
    console.error('Login API Error:', error);
    return NextResponse.json({ success: false, error: error.message }, { status: 500 });
  }
}
