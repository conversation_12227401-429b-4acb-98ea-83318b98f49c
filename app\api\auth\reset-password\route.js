import { NextResponse } from 'next/server';
import { auth } from '@/lib/firebase';
import { sendPasswordResetEmail } from 'firebase/auth';

export async function POST(request) {
  try {
    const { email } = await request.json();
    await sendPasswordResetEmail(auth, email);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Password reset email sent' 
    });
  } catch (error) {
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}