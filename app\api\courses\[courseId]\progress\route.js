import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { doc, getDoc, setDoc, collection } from 'firebase/firestore';

// Get course progress for a user
export async function GET(request, { params }) {
  try {
    const { courseId } = params;
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'User ID is required'
      }, { status: 400 });
    }

    const progressDoc = await getDoc(doc(db, 'userProgress', `${userId}_${courseId}`));
    
    if (!progressDoc.exists()) {
      return NextResponse.json({
        success: true,
        progress: {
          completedModules: [],
          currentModule: null,
          gameScores: {},
          quizResults: []
        }
      });
    }

    return NextResponse.json({
      success: true,
      progress: progressDoc.data()
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Initialize or update course progress
export async function POST(request, { params }) {
  try {
    const { courseId } = params;
    const { userId, moduleId, gameScore, quizResult } = await request.json();

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'User ID is required'
      }, { status: 400 });
    }

    const progressRef = doc(db, 'userProgress', `${userId}_${courseId}`);
    const progressDoc = await getDoc(progressRef);
    
    let progress = progressDoc.exists() ? progressDoc.data() : {
      userId,
      courseId,
      completedModules: [],
      currentModule: null,
      gameScores: {},
      quizResults: []
    };

    // Update progress data
    if (moduleId) {
      if (!progress.completedModules.includes(moduleId)) {
        progress.completedModules.push(moduleId);
      }
      progress.currentModule = moduleId;
    }

    if (gameScore) {
      const currentScore = progress.gameScores[gameScore.gameId];
      progress.gameScores[gameScore.gameId] = {
        highScore: currentScore ? Math.max(currentScore.highScore, gameScore.score) : gameScore.score,
        attempts: (currentScore?.attempts || 0) + 1,
        lastPlayed: new Date().toISOString()
      };
    }

    if (quizResult) {
      progress.quizResults.push({
        ...quizResult,
        completedAt: new Date().toISOString()
      });
    }

    await setDoc(progressRef, progress);

    return NextResponse.json({
      success: true,
      progress
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}