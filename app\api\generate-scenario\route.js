import { NextResponse } from 'next/server';
import { GoogleGenAI } from '@google/genai';
import { GoogleGenAI } from '@google/genai';

/**
 * Generate an alternate timeline fiction scenario using Gemini
 * @route POST /api/generate-scenario
 */
export async function POST(request) {
  try {
    // Get request body
    const {
      bookTitle,
      author,
      changeLocation,
      whatIfPrompt
    } = await request.json();

    // Validate required fields
    if (!bookTitle || !author || !whatIfPrompt) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Initialize the Gemini API client
    const apiKey = process.env.GOOGLE_API_KEY;
    if (!apiKey) {
      return NextResponse.json(
        { success: false, error: 'API key not configured' },
        { status: 500 }
      );
    }

    const genAI = new GoogleGenAI({ apiKey });
    const model = 'gemini-2.0-flash-001';

    // Construct the system prompt
    const systemPrompt = `You are an expert creative writer specializing in alternate timeline fiction and interactive storytelling. Your task is to generate engaging "what if" scenarios based on classic literature.

IMPORTANT: You must respond with ONLY valid JSON format. Do not include any markdown formatting, code blocks, or explanatory text. The JSON should be directly parseable by JSON.parse().

The scenario should feel authentic to the author's writing style and world-building. You will be given a book title, author, and a specific change point. Your job is to imagine how the story would unfold differently based on this change point, without requiring knowledge of the original event.`;

    // Construct the user prompt
    const userPrompt = `TASK: Generate an interactive alternate scenario for the following book change point.
BOOK INFORMATION:
Title: ${bookTitle}
Author: ${author}
Chapter/Section: ${changeLocation || 'Unknown'}

CHANGE POINT:
Change: ${whatIfPrompt}

REQUIREMENTS: Create an interactive scenario that explores the immediate consequences of this change.

OUTPUT FORMAT:
{
"scenario": {
    "title": "[Engaging title for this alternate timeline]"
  },
"scene_visuals": {
    "background_description": "[Detailed description for image generation: setting, time of day, lighting, key visual elements]",
    "character_state": "[Description of main character's appearance, expression, and body language in this moment]",
    "scene_mood": "[Visual mood: colors, lighting, atmosphere for image generation]"
  },
"screens": [
    {
      "id": 1,
      "text": "[Initial 1-2 sentences of the alternate scenario]"
    },
    {
      "id": 2,
      "text": "[Next 3-4 sentences of the alternate scenario]"
    },
    {
      "id": 3,
      "choice_text": "[Rest of the alternate scenario]"
     }
  ],
}

STYLE GUIDELINES:
- Match the original author's writing style and tone
- Make the narrative engaging and immersive
- Focus on character development and emotional resonance

RESPONSE FORMAT REQUIREMENTS:
- Respond with ONLY the JSON object, no markdown formatting or code blocks
- The JSON must be directly parseable by JSON.parse()
- Do not include any explanatory text before or after the JSON`;

    // Generate content with the model
    const result = await genAI.models.generateContent({
      model,
      contents: [
        { role: 'user', parts: [{ text: systemPrompt }] },
        { role: 'model', parts: [{ text: 'I understand. I will generate alternate timeline fiction scenarios in valid JSON format only, with no additional text or markdown formatting.' }] },
        { role: 'user', parts: [{ text: userPrompt }] }
      ],
      config: {
        temperature: 0.5, // Lower temperature for more predictable output
        topP: 0.9,
        topK: 40,
        maxOutputTokens: 2048,
        responseFormat: { type: "json" }, // Request JSON response format
      },
    });

    // Log the response structure to understand its format
    console.log('Response structure:', JSON.stringify(result, null, 2));

    // Extract text from the response based on the new SDK structure
    let text = '';

    try {
      // The structure might be different depending on the SDK version
      if (result.candidates && result.candidates.length > 0) {
        const candidate = result.candidates[0];
        if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
          text = candidate.content.parts[0].text || '';
        }
      }

      // If we couldn't extract text using the expected structure, try to stringify the result
      if (!text) {
        text = JSON.stringify(result);
      }
    } catch (e) {
      console.error('Error extracting text from response:', e);
      text = 'Unable to extract text from response';
    }

    console.log('Raw response text:', text);

    // Parse the JSON response
    try {
      let parsedResponse;

      // First, check if the text is already a valid JSON object
      if (typeof text === 'object' && text !== null) {
        parsedResponse = text;
        console.log('Response is already a JSON object');
      } else {
        let jsonString = text;

        // Try to extract JSON if it's wrapped in markdown code blocks
        const jsonBlockMatch = text.match(/```json\n([\s\S]*?)\n```/) || text.match(/```\n([\s\S]*?)\n```/);
        if (jsonBlockMatch && jsonBlockMatch[1]) {
          jsonString = jsonBlockMatch[1];
        }

        // Try to extract just the JSON object if there's text before or after it
        const jsonObjectMatch = jsonString.match(/(\{[\s\S]*\})/);
        if (jsonObjectMatch && jsonObjectMatch[1]) {
          jsonString = jsonObjectMatch[1];
        }

        console.log('Extracted JSON string:', jsonString);

        // Parse the JSON
        parsedResponse = JSON.parse(jsonString);
      }

      return NextResponse.json({
        success: true,
        data: parsedResponse,
        rawResponse: text
      });
    } catch (parseError) {
      console.error('Error parsing Gemini response as JSON:', parseError);

      // Attempt to create a valid JSON structure from the text response
      try {
        // Create a simple valid JSON structure with the text
        const fallbackResponse = {
          scenario: {
            title: "Generated Alternate Timeline"
          },
          scene_visuals: {
            background_description: "A scene based on the provided context",
            character_state: "Characters in a moment of change",
            scene_mood: "Dramatic, transformative"
          },
          screens: [
            {
              id: 1,
              text: "The model didn't return valid JSON. Here's the raw response:"
            },
            {
              id: 2,
              text: text.substring(0, 500) // First 500 chars of the response
            },
            {
              id: 3,
              choice_text: text.length > 500 ? text.substring(500) : "End of response"
            }
          ]
        };

        return NextResponse.json({
          success: true,
          data: fallbackResponse,
          rawResponse: text,
          note: "Used fallback JSON structure due to parsing error"
        });
      } catch (fallbackError) {
        return NextResponse.json({
          success: false,
          error: 'Failed to parse response as JSON',
          rawResponse: text
        }, { status: 500 });
      }
    }
  } catch (error) {
    console.error('Error generating alternate scenario with Gemini:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Unknown error occurred'
    }, { status: 500 });
  }
}
