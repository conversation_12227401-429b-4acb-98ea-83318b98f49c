// app/api/modules/[courseId]/[moduleId]/route.js
import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { doc, getDoc } from 'firebase/firestore';

export async function GET(request, { params }) {
  try {
    // Ensure params is awaited if needed
    const resolvedParams = params instanceof Promise ? await params : params;
    const { courseId, moduleId } = resolvedParams;
    
    console.log(`Fetching module: ${moduleId} for course: ${courseId}`);
    
    if (!courseId || !moduleId) {
      return NextResponse.json({
        success: false,
        error: 'Course ID and Module ID are required'
      }, { status: 400 });
    }
    
    // Get the module document
    const moduleDoc = await getDoc(doc(db, 'modules', moduleId));
    
    if (!moduleDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Module not found'
      }, { status: 404 });
    }
    
    // Get module data
    const moduleData = moduleDoc.data();
    
    // Verify this module belongs to the requested course
    if (moduleData.courseId !== courseId) {
      return NextResponse.json({
        success: false,
        error: 'Module does not belong to the specified course'
      }, { status: 403 });
    }
    
    return NextResponse.json({
      success: true,
      module: {
        id: moduleDoc.id,
        ...moduleData
      }
    });
  } catch (error) {
    console.error(`Error in module API:`, error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}