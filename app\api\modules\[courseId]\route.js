import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { 
  collection, 
  query, 
  where, 
  getDocs, 
  doc, 
  getDoc, 
  addDoc, 
  updateDoc,
  orderBy 
} from 'firebase/firestore';

// Get course modules
export async function GET(request, { params }) {
  try {
    const { courseId } = params;
    
    console.log(`Fetching modules for course: ${courseId}`);
    
    if (!courseId) {
      return NextResponse.json({
        success: false,
        error: 'Course ID is required'
      }, { status: 400 });
    }
    
    // First check if the course exists
    const courseDoc = await getDoc(doc(db, 'courses', courseId));
    if (!courseDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Course not found'
      }, { status: 404 });
    }
    
    // Get all modules with matching courseId
    const modulesQuery = query(
      collection(db, 'modules'),
      where('courseId', '==', courseId),
      orderBy('order', 'asc')
    );
    
    const modulesSnapshot = await getDocs(modulesQuery);
    
    // Convert to array
    let modules = [];
    modulesSnapshot.forEach(doc => {
      modules.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    console.log(`Found ${modules.length} modules for course ${courseId}`);
    
    // Ensure order is continuous (1, 2, 3...)
    modules = modules.map((module, index) => ({
      ...module,
      // Convert to two-digit string format: 01, 02, 03...
      displayOrder: String(index + 1).padStart(2, '0')
    }));
    
    return NextResponse.json({
      success: true,
      modules
    });
  } catch (error) {
    console.error(`Error in modules API:`, error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// Add/Update module
export async function POST(request, { params }) {
  try {
    const { courseId } = params;
    const moduleData = await request.json();
    const { title, order, gameId } = moduleData;
    
    // Validate required fields
    if (!title || !courseId || !gameId) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields (title, courseId, gameId)'
      }, { status: 400 });
    }
    
    // Validate that the course exists
    const courseDoc = await getDoc(doc(db, 'courses', courseId));
    if (!courseDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'Course not found'
      }, { status: 404 });
    }
    
    // Get existing modules to determine next order if not provided
    let newOrder = order;
    if (!newOrder) {
      const modulesQuery = query(
        collection(db, 'modules'),
        where('courseId', '==', courseId),
        orderBy('order', 'desc')
      );
      
      const modulesSnapshot = await getDocs(modulesQuery);
      const modules = [];
      modulesSnapshot.forEach(doc => {
        modules.push(doc.data());
      });
      
      // If modules exist, set order to one more than highest existing order
      if (modules.length > 0) {
        newOrder = parseInt(modules[0].order) + 1;
      } else {
        // First module for this course
        newOrder = 1;
      }
    }
    
    // Format order as two-digit string: 01, 02, 03...
    const formattedOrder = String(newOrder).padStart(2, '0');
    
    // If moduleId is provided, update existing module
    if (moduleData.id) {
      const moduleRef = doc(db, 'modules', moduleData.id);
      await updateDoc(moduleRef, {
        ...moduleData,
        courseId,
        order: formattedOrder,
        updatedAt: new Date().toISOString()
      });
      
      return NextResponse.json({
        success: true,
        message: 'Module updated successfully',
        moduleId: moduleData.id
      });
    }
    
    // Create new module
    const newModule = {
      title,
      courseId,
      order: formattedOrder,
      gameId,
      description: moduleData.description || '',
      duration: moduleData.duration || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    const moduleRef = await addDoc(collection(db, 'modules'), newModule);
    
    return NextResponse.json({
      success: true,
      message: 'Module created successfully',
      moduleId: moduleRef.id
    });
  } catch (error) {
    console.error(`Error in adding/updating module:`, error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}