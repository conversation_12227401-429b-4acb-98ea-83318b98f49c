import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, query, orderBy, limit, getDocs } from 'firebase/firestore';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'xp'; // xp, course, game
    const courseId = searchParams.get('courseId');
    const gameId = searchParams.get('gameId');
    const limitSize = parseInt(searchParams.get('limit') || '10');

    let leaderboardData = [];

    switch (type) {
      case 'xp':
        leaderboardData = await getXPLeaderboard(limitSize);
        break;
      case 'course':
        if (!courseId) {
          return NextResponse.json({
            success: false,
            error: 'Course ID is required for course leaderboard'
          }, { status: 400 });
        }
        leaderboardData = await getCourseLeaderboard(courseId, limitSize);
        break;
      case 'game':
        if (!gameId) {
          return NextResponse.json({
            success: false,
            error: 'Game ID is required for game leaderboard'
          }, { status: 400 });
        }
        leaderboardData = await getGameLeaderboard(gameId, limitSize);
        break;
      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid leaderboard type'
        }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      leaderboard: leaderboardData
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

async function getXPLeaderboard(limitSize) {
  const usersQuery = query(
    collection(db, 'users'),
    orderBy('progress.xp', 'desc'),
    limit(limitSize)
  );
  
  const snapshot = await getDocs(usersQuery);
  return snapshot.docs.map(doc => ({
    userId: doc.id,
    displayName: doc.data().displayName,
    xp: doc.data().progress?.xp || 0,
    level: doc.data().progress?.currentLevel || 1
  }));
}

async function getCourseLeaderboard(courseId, limitSize) {
  const progressQuery = query(
    collection(db, 'userProgress'),
    where('courseId', '==', courseId),
    orderBy('completedModules', 'desc'),
    limit(limitSize)
  );

  const snapshot = await getDocs(progressQuery);
  const leaderboard = [];

  for (const doc of snapshot.docs) {
    const userData = await getDoc(doc(db, 'users', doc.data().userId));
    if (userData.exists()) {
      leaderboard.push({
        userId: doc.data().userId,
        displayName: userData.data().displayName,
        completedModules: doc.data().completedModules?.length || 0,
        averageScore: calculateAverageScore(doc.data().quizResults || [])
      });
    }
  }

  return leaderboard;
}

async function getGameLeaderboard(gameId, limitSize) {
  const progressQuery = query(
    collection(db, 'userProgress'),
    where(`gameScores.${gameId}.highScore`, '>', 0),
    orderBy(`gameScores.${gameId}.highScore`, 'desc'),
    limit(limitSize)
  );

  const snapshot = await getDocs(progressQuery);
  const leaderboard = [];

  for (const doc of snapshot.docs) {
    const userData = await getDoc(doc(db, 'users', doc.data().userId));
    if (userData.exists()) {
      leaderboard.push({
        userId: doc.data().userId,
        displayName: userData.data().displayName,
        highScore: doc.data().gameScores[gameId].highScore,
        attempts: doc.data().gameScores[gameId].attempts
      });
    }
  }

  return leaderboard;
}

function calculateAverageScore(quizResults) {
  if (quizResults.length === 0) return 0;
  const totalScore = quizResults.reduce((sum, quiz) => sum + quiz.score, 0);
  return Math.round(totalScore / quizResults.length);
}