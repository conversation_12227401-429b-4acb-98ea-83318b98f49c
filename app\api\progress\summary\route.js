import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, query, where, getDocs } from 'firebase/firestore';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'User ID is required'
      }, { status: 400 });
    }

    const userDoc = await getDoc(doc(db, 'users', userId));
    if (!userDoc.exists()) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      }, { status: 404 });
    }

    const progressQuery = query(
      collection(db, 'userProgress'),
      where('userId', '==', userId)
    );
    const progressSnapshot = await getDocs(progressQuery);

    const progressData = progressSnapshot.docs.map(doc => ({
      courseId: doc.data().courseId,
      completedModules: doc.data().completedModules || [],
      gameScores: doc.data().gameScores || {},
      quizResults: doc.data().quizResults || []
    }));

    const summary = {
      totalXP: userDoc.data().progress?.xp || 0,
      level: userDoc.data().progress?.currentLevel || 1,
      coursesStarted: progressData.length,
      coursesCompleted: progressData.filter(p => p.completedModules.length > 0).length,
      totalGamesPlayed: progressData.reduce((total, p) => {
        return total + Object.values(p.gameScores).reduce((sum, game) => sum + game.attempts, 0);
      }, 0),
      averageQuizScore: calculateAverageQuizScore(progressData)
    };

    return NextResponse.json({
      success: true,
      summary
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

function calculateAverageQuizScore(progressData) {
  const allQuizzes = progressData.reduce((acc, p) => [...acc, ...(p.quizResults || [])], []);
  if (allQuizzes.length === 0) return 0;
  
  const totalScore = allQuizzes.reduce((sum, quiz) => sum + quiz.score, 0);
  return Math.round(totalScore / allQuizzes.length);
}