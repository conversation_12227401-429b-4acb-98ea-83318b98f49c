// app/api/subscribe/route.js
import { NextResponse } from 'next/server';
import { addToWaitlist, checkEmailExists, getWaitlistPosition, reactivateUser } from '@/lib/database';
import { sendThankYouEmail } from '@/lib/email';
import { isDevelopment, currentEnv } from '@/lib/config';

export async function POST(request) {
  try {
    const { email, referralCode } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    try {
      // Check if email exists and get status
      const { exists, status, docId } = await checkEmailExists(email);
      
      if (exists) {
        if (status === 'active') {
          return NextResponse.json(
            { error: 'Email already registered' },
            { status: 409 }
          );
        }

        if (status === 'unsubscribed') {
          // Reactivate the user and get new position
          const newPosition = await reactivateUser(docId);
          
          return NextResponse.json({
            success: true,
            message: 'You have been resubscribed to the waitlist.',
            resubscribed: true,
            position: newPosition
          });
        }
      }

      // For new users, proceed with normal signup
      const userData = await addToWaitlist(email, referralCode);

      // Send welcome email
      await sendThankYouEmail(
        email, 
        userData.position, 
        userData.referralCode,
        userData.id
      );

      return NextResponse.json({
        success: true,
        message: 'Successfully subscribed to waitlist',
        environment: isDevelopment() ? 'development' : 'production',
        position: userData.position
      });

    } catch (error) {
      console.error(`Error in ${currentEnv} environment:`, error);
      throw error;
    }

  } catch (error) {
    console.error('Error processing subscription:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process subscription',
        details: isDevelopment() ? error.message : undefined
      },
      { status: 500 }
    );
  }
}