import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, query, where, getDocs, updateDoc } from 'firebase/firestore';
import { TABLES } from '@/lib/config';
import { generateToken } from '@/lib/tokenUtils';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email')?.toLowerCase();
    const token = searchParams.get('token');

    if (!email || !token) {
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Generate and verify token
    const expectedToken = await generateToken(email);
    if (token !== expectedToken) {
      console.log('Token mismatch:', { received: token, expected: expectedToken });
      return NextResponse.json(
        { error: 'Invalid unsubscribe token' },
        { status: 403 }
      );
    }

    // Find and update user
    const waitlistRef = collection(db, TABLES.WAITLIST);
    const q = query(waitlistRef, where('email', '==', email));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return NextResponse.json(
        { error: 'Email not found' },
        { status: 404 }
      );
    }

    // Update the user's status
    const userDoc = querySnapshot.docs[0];
    await updateDoc(userDoc.ref, {
      status: 'unsubscribed',
      unsubscribedAt: new Date().toISOString()
    });

    // Redirect to confirmation page
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_SITE_URL}/unsubscribed?email=${encodeURIComponent(email)}`
    );

  } catch (error) {
    console.error('Error processing unsubscribe request:', error);
    return NextResponse.json(
      { error: 'Failed to process unsubscribe request' },
      { status: 500 }
    );
  }
}