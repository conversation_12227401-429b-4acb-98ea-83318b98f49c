// app/api/upload/route.js
import { NextResponse } from 'next/server';
import { ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import { storage } from '@/lib/firebase';

export async function POST(request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    
    if (!file) {
      return NextResponse.json({ success: false, error: 'No file provided' }, { status: 400 });
    }

    // Get file data
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    // Create file name - you could customize this
    const fileExtension = file.name.split('.').pop();
    const fileName = `${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExtension}`;
    
    // Specify path in Firebase Storage
    const storageRef = ref(storage, `uploads/${fileName}`);
    
    // Create upload task
    const metadata = {
      contentType: file.type,
    };
    
    // Upload the file
    const snapshot = await uploadBytesResumable(storageRef, buffer, metadata);
    
    // Get download URL
    const downloadURL = await getDownloadURL(snapshot.ref);
    
    return NextResponse.json({ 
      success: true, 
      url: downloadURL,
      fileName: fileName,
      contentType: file.type,
      size: file.size
    });
    
  } catch (error) {
    console.error('Error uploading file:', error);
    return NextResponse.json({ 
      success: false, 
      error: error.message || 'Failed to upload file' 
    }, { status: 500 });
  }
}