import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { doc, getDoc, updateDoc, arrayUnion } from 'firebase/firestore';

// Get user achievements
export async function GET(request, { params }) {
  try {
    const { userId } = params;
    
    const userDoc = await getDoc(doc(db, 'users', userId));
    if (!userDoc.exists()) {
      return NextResponse.json({ 
        success: false, 
        error: 'User not found' 
      }, { status: 404 });
    }

    const achievementIds = userDoc.data()?.progress?.achievementIds || [];
    const achievements = [];

    for (const achievementId of achievementIds) {
      const achievementDoc = await getDoc(doc(db, 'achievements', achievementId));
      if (achievementDoc.exists()) {
        achievements.push({
          id: achievementDoc.id,
          ...achievementDoc.data()
        });
      }
    }

    return NextResponse.json({ 
      success: true, 
      achievements 
    });
  } catch (error) {
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}

// Add new achievement
export async function POST(request, { params }) {
  try {
    const { userId } = params;
    const { achievementId } = await request.json();

    // Verify achievement exists
    const achievementDoc = await getDoc(doc(db, 'achievements', achievementId));
    if (!achievementDoc.exists()) {
      return NextResponse.json({ 
        success: false, 
        error: 'Achievement not found' 
      }, { status: 404 });
    }

    // Add achievement to user's progress
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      'progress.achievementIds': arrayUnion(achievementId),
      'progress.xp': increment(achievementDoc.data().reward.xp)
    });

    // Get updated user data
    const updatedDoc = await getDoc(userRef);
    return NextResponse.json({ 
      success: true, 
      progress: updatedDoc.data().progress 
    });
  } catch (error) {
    return NextResponse.json({ 
      success: false, 
      error: error.message 
    }, { status: 500 });
  }
}