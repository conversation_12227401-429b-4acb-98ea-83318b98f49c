'use client';

import { useEffect, useRef, useState } from 'react';

export default function GamePage() {
  const canvasRef = useRef(null);
  const gameContainerRef = useRef(null);
  const [gameStatus, setGameStatus] = useState('loading');
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [statusMessage, setStatusMessage] = useState('Initializing game...');
  const [gameEngine, setGameEngine] = useState(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    let isComponentMounted = true;

    // Load the Godot engine script
    const loadGodotEngine = async () => {
      try {
        // Check if Engine is already loaded
        if (typeof window !== 'undefined' && window.Engine) {
          if (isComponentMounted) {
            initializeGame();
          }
          return;
        }

        // Set up Module configuration BEFORE loading the script
        if (typeof window !== 'undefined') {
          console.log('Setting up Module configuration...');
          window.Module = {
            locateFile: function(path, prefix) {
              console.log('Module.locateFile called with:', path, 'prefix:', prefix);

              // Map the expected file names to our actual file names
              if (path === 'godot.web.template_debug.wasm32.wasm') {
                console.log('Redirecting WASM file from', path, 'to /godot.web.template_debug.wasm32.wasm (which will be rewritten to /index.wasm)');
                return '/godot.web.template_debug.wasm32.wasm';
              }
              if (path === 'index.pck') {
                console.log('Using PCK file at /index.pck');
                return '/index.pck';
              }
              if (path === 'index.service.worker.js') {
                console.log('Using service worker at /index.service.worker.js');
                return '/index.service.worker.js';
              }

              // For other files, use the default behavior
              console.log('Using default path resolution for:', path);
              return prefix + path;
            },
            // Add additional debugging
            print: function(text) {
              console.log('Godot print:', text);
            },
            printErr: function(text) {
              console.error('Godot error:', text);
            }
          };
          console.log('Module configuration set up successfully');
        }

        // Create script element to load Godot engine
        const script = document.createElement('script');
        script.src = '/index.js';
        script.onload = () => {
          if (isComponentMounted) {
            initializeGame();
          }
        };
        script.onerror = () => {
          if (isComponentMounted) {
            setGameStatus('error');
            setStatusMessage('Failed to load game engine. Please check your internet connection and try again.');
          }
        };
        document.head.appendChild(script);
      } catch (error) {
        console.error('Error loading Godot engine:', error);
        if (isComponentMounted) {
          setGameStatus('error');
          setStatusMessage('Failed to initialize game');
        }
      }
    };

    const initializeGame = () => {
      if (typeof window === 'undefined' || !window.Engine) {
        setGameStatus('error');
        setStatusMessage('Game engine not available');
        return;
      }

      const GODOT_CONFIG = {
        args: [],
        canvasResizePolicy: 2,
        executable: "index",
        experimentalVK: false,
        fileSizes: { "index.pck": 88208, "index.wasm": 49282035 },
        focusCanvas: true,
        gdextensionLibs: [],
        serviceWorker: "index.service.worker.js"
      };

      const engine = new window.Engine(GODOT_CONFIG);
      setGameEngine(engine);

      // Check for missing features
      const missing = window.Engine.getMissingFeatures();
      if (missing.length !== 0) {
        const missingMsg = 'The following features required to run the game are missing:\n' + missing.join('\n');
        setGameStatus('error');
        setStatusMessage(missingMsg);
        return;
      }

      // Initialize the engine first, then start the game
      setGameStatus('loading');
      setStatusMessage('Initializing game engine...');

      console.log('Initializing Godot engine with base path "/"');

      // First, initialize the engine with proper base path
      engine.init('/').then(() => {
        console.log('Engine initialized successfully, starting game...');
        setStatusMessage('Loading game assets...');

        // Wait a moment for initialization to complete
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve();
          }, 100);
        });
      }).then(() => {
        // Now start the game
        return engine.startGame({
          canvas: canvasRef.current,
          onProgress: (current, total) => {
            if (total > 0) {
              const progress = (current / total) * 100;
              setLoadingProgress(progress);
              setStatusMessage(`Loading... ${Math.round(progress)}%`);

              if (current === total) {
                setTimeout(() => {
                  setGameStatus('running');
                  setStatusMessage('');
                }, 500);
              }
            }
          },
          // Add exit handler for better error handling
          onExit: () => {
            console.log('Game exited');
            setGameStatus('error');
            setStatusMessage('Game exited unexpectedly');
          }
        });
      }).then(() => {
        console.log('Game started successfully');
        setGameStatus('running');
        setStatusMessage('');
      }).catch((error) => {
        console.error('Game initialization error:', error);
        setGameStatus('error');

        // Provide more specific error messages
        let errorMessage = 'Failed to start game';
        if (error.message) {
          if (error.message.includes('initialized before it can be started')) {
            errorMessage = 'Engine initialization failed. Please refresh the page and try again.';
          } else if (error.message.includes('base path') || error.message.includes('init')) {
            errorMessage = 'Game files not found. Please ensure all game assets are properly loaded.';
          } else if (error.message.includes('WebGL')) {
            errorMessage = 'WebGL not supported or context lost. Please try refreshing the page or use a different browser.';
          } else if (error.message.includes('.pck')) {
            errorMessage = 'Game data file missing. Please refresh the page to reload game assets.';
          } else if (error.message.includes('shader') || error.message.includes('buffer')) {
            errorMessage = 'Graphics compatibility issue. Your device may not support all game features.';
          } else {
            errorMessage = 'Failed to start game: ' + error.message;
          }
        }

        setStatusMessage(errorMessage);
        initializationRef.current = false; // Reset flag on error
      });
    };

    loadGodotEngine();

    // Cleanup function
    return () => {
      isComponentMounted = false;
      if (gameEngine) {
        // Clean up game engine if needed
        try {
          gameEngine.requestQuit();
        } catch (error) {
          console.warn('Error during game cleanup:', error);
        }
      }
    };
  }, []); // Empty dependency array - only run once on mount

  // Fullscreen functionality
  const toggleFullscreen = async () => {
    if (!gameContainerRef.current) return;

    try {
      if (!document.fullscreenElement) {
        await gameContainerRef.current.requestFullscreen();
        setIsFullscreen(true);
      } else {
        await document.exitFullscreen();
        setIsFullscreen(false);
      }
    } catch (error) {
      console.warn('Fullscreen toggle failed:', error);
    }
  };

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  const renderLoadingIndicator = () => {
    if (gameStatus === 'loading') {
      return (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-75 z-10">
          <div className="text-white text-center">
            <div className="mb-4">
              <div className="w-64 h-2 bg-gray-700 rounded-full overflow-hidden">
                <div
                  className="h-full bg-green-500 transition-all duration-300 ease-out"
                  style={{ width: `${loadingProgress}%` }}
                />
              </div>
            </div>
            <p className="text-sm">{statusMessage}</p>
          </div>
        </div>
      );
    }

    if (gameStatus === 'error') {
      return (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-75 z-10">
          <div className="text-white text-center max-w-md px-4">
            <div className="mb-4">
              <svg className="w-16 h-16 mx-auto text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">Game Loading Error</h3>
            <p className="text-sm text-gray-300 whitespace-pre-line">{statusMessage}</p>
            <div className="mt-4 space-x-2">
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors"
              >
                Reload Page
              </button>
              <button
                onClick={() => {
                  // Reset initialization flag and reload page for clean retry
                  window.location.reload();
                }}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors"
              >
                Retry Game
              </button>
            </div>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="min-h-screen bg-app-pattern flex flex-col">
      {/* Header */}
      <header className="bg-[var(--primary-green)] text-white p-4 relative z-30">
        <div className="container mx-auto flex items-center justify-between">
          <h1 className="text-2xl font-bold font-slackey">The Money Tales</h1>
          <nav className="flex space-x-4">
            <a href="/" className="hover:text-green-200 transition-colors">Home</a>
            <a href="/game" className="text-green-200">Game</a>
          </nav>
        </div>

        {/* Game Status Indicator */}
        {gameStatus !== 'running' && (
          <div className="absolute top-full left-0 right-0 bg-yellow-600 text-white text-center py-1 text-sm">
            {gameStatus === 'loading' ? 'Loading Game...' : gameStatus === 'error' ? 'Game Error' : 'Initializing...'}
          </div>
        )}
      </header>

      {/* Game Container */}
      <main className="flex-1 relative overflow-hidden">
        <div
          ref={gameContainerRef}
          className="w-full h-full relative"
          style={{ minHeight: 'calc(100vh - 120px)' }}
        >
          {/* Game Controls */}
          {gameStatus === 'running' && (
            <div className="absolute top-4 right-4 z-20 flex space-x-2">
              <button
                onClick={toggleFullscreen}
                className="bg-black bg-opacity-50 hover:bg-opacity-75 text-white p-2 rounded-md transition-all"
                title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
              >
                {isFullscreen ? (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                  </svg>
                )}
              </button>
            </div>
          )}

          <canvas
            ref={canvasRef}
            id="game-canvas"
            className="block w-full h-full bg-black"
            style={{
              display: 'block',
              margin: 0,
              color: 'white',
              touchAction: 'none',
              outline: 'none'
            }}
            tabIndex={0}
          >
            HTML5 canvas appears to be unsupported in the current browser.
            <br />
            Please try updating or use a different browser.
          </canvas>

          {renderLoadingIndicator()}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 text-white p-4 text-center">
        <p className="text-sm">
          © 2024 The Money Tales. Experience financial literacy through interactive storytelling.
        </p>
      </footer>
    </div>
  );
}