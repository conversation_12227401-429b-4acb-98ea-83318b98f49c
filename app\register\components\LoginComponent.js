  'use client';

import { useState } from 'react';

export default function LoginComponent({ onBack }) {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // TODO: Implement login logic
      console.log('Login data:', formData);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (err) {
      setError(err.message || 'Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement Google login
      console.log('Google login');
    } catch (err) {
      setError(err.message || 'Google login failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col md:flex-row h-[100dvh] md:min-h-screen bg-app-pattern overflow-hidden">
      {/* Left Panel - Form */}
      <div className="w-full md:w-1/2 p-4 md:p-6 flex flex-col justify-center relative">
        {/* Back Button */}
        <button
          onClick={() => onBack('choice')}
          className="absolute top-4 left-4 md:top-6 md:left-6 text-[#696F79] hover:text-white transition-colors duration-200 z-20 text-sm md:text-base"
        >
          ← Back
        </button>

        <div className="max-w-sm mx-auto w-full space-y-4 mt-12 md:mt-0">
          {/* Header */}
          <div className="space-y-1">
            <h1 className="text-2xl md:text-3xl font-bold text-white font-inter">
              Welcome Back
            </h1>
            <p className="text-[#696F79] text-sm font-inter">
              Sign in to your account to continue
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-2">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-3">
            {/* Email */}
            <div>
              <label className="block text-[#696F79] text-sm mb-1 font-inter">
                Your email*
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter your email"
                required
                className="w-full px-3 py-2.5 bg-[#3a3d42] border border-[#4a4d52] rounded-md text-white placeholder-[#696F79] focus:outline-none focus:border-[#696F79] transition-colors duration-200 text-sm font-inter"
              />
            </div>

            {/* Password */}
            <div>
              <label className="block text-[#696F79] text-sm mb-1 font-inter">
                Password*
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="Enter password"
                  required
                  className="w-full px-3 py-2.5 bg-[#3a3d42] border border-[#4a4d52] rounded-md text-white placeholder-[#696F79] focus:outline-none focus:border-[#696F79] transition-colors duration-200 pr-10 text-sm font-inter"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-2.5 top-1/2 transform -translate-y-1/2 text-[#696F79] hover:text-white transition-colors duration-200 text-sm"
                >
                  {showPassword ? '👁️' : '👁️‍🗨️'}
                </button>
              </div>
            </div>

            {/* Forgot Password */}
            <div className="text-right">
              <button
                type="button"
                className="text-[#696F79] hover:text-white transition-colors duration-200 text-xs font-inter"
              >
                Forgot Password?
              </button>
            </div>

            {/* Login Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-2.5 px-4 bg-[#8B5CF6] text-white rounded-md font-medium hover:bg-[#7C3AED] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm font-inter"
            >
              {isLoading ? 'Signing in...' : 'Log in'}
            </button>
          </form>

          {/* Divider */}
          <div className="flex items-center space-x-3">
            <div className="flex-1 h-px bg-[#4a4d52]"></div>
            <span className="text-[#696F79] text-xs font-inter">Or</span>
            <div className="flex-1 h-px bg-[#4a4d52]"></div>
          </div>

          {/* Google Login */}
          <button
            onClick={handleGoogleLogin}
            disabled={isLoading}
            className="w-full py-2.5 px-4 bg-[#3a3d42] border border-[#4a4d52] text-white rounded-md font-medium hover:bg-[#4a4d52] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 text-sm font-inter"
          >
            <svg className="w-4 h-4" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            <span>Login with Google</span>
          </button>

          {/* Sign Up Link */}
          <div className="text-center">
            <span className="text-[#696F79] text-xs font-inter">New here? </span>
            <button
              onClick={() => onBack('signup')}
              className="text-white hover:text-[#696F79] transition-colors duration-200 text-xs font-medium font-inter"
            >
              Sign up
            </button>
          </div>
        </div>
      </div>

      {/* Right Panel - Classical Painting with rounded corners */}
      <div className="hidden md:flex md:w-1/2 items-center justify-start p-4">
        <div className="relative w-full h-full max-h-[90vh] rounded-2xl overflow-hidden bg-gradient-to-br from-[#3a3d42] to-[#2a2d32] shadow-2xl">
          {/* Sample classical painting - replace with actual image */}
          <img
            src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=800&fit=crop&crop=center"
            alt="Classical painting"
            className="w-full h-full object-cover"
            onError={(e) => {
              // Fallback if image doesn't load
              e.target.style.display = 'none';
              e.target.nextSibling.style.display = 'flex';
            }}
          />
          {/* Fallback placeholder */}
          <div className="absolute inset-0 hidden items-center justify-center">
            <div className="text-center space-y-4 p-8">
              <div className="w-32 h-32 mx-auto bg-[#696F79]/20 rounded-full flex items-center justify-center">
                <svg className="w-16 h-16 text-[#696F79]" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
                </svg>
              </div>
              <p className="text-[#696F79] text-sm">Classical painting</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
