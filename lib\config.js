const ENV = process.env.NEXT_PUBLIC_APP_ENV || 'development';

// Database table configurations
const DB_TABLES = {
  WAITLIST: 'waitlist',
  USERS: 'users'
};

const ENV_CONFIG = {
    development: {
        firebaseConfig: {
        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
        appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
        }
    },
    production: {
        prefix: '',
        firebaseConfig: {
        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
        appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
        }
    }
};

export const getTableName = (tableName) => {
const config = ENV_CONFIG[ENV] || ENV_CONFIG.development;
return `${config.prefix}${tableName}`;
};

export const getTables = () => {
return Object.entries(DB_TABLES).reduce((acc, [key, value]) => {
    acc[key] = getTableName(value);
    return acc;
}, {});
};

export const TABLES = getTables();

export const isDevelopment = () => ENV !== 'production';

export const currentEnv = ENV;

export const getFirebaseConfig = () => ENV_CONFIG[ENV]?.firebaseConfig;

if (isDevelopment()) {
    console.log('Current Environment:', currentEnv);
    console.log('Using Tables:', TABLES);
  }