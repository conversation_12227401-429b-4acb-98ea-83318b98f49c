import { 
    collection, 
    query, 
    where, 
    getDocs, 
    addDoc, 
    updateDoc, 
    doc,
    increment,
    getCountFromServer 
} from 'firebase/firestore';
import { db } from './firebase';
import { TABLES, isDevelopment } from './config';
import crypto from 'crypto';

export const checkEmailExists = async (email) => {
    const waitlistRef = collection(db, TABLES.WAITLIST);
    const q = query(waitlistRef, where('email', '==', email.toLowerCase()));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return { exists: false };
    }
  
    const docData = querySnapshot.docs[0];
    return {
      exists: true,
      status: docData.data().status || 'active', // default to active for older entries
      docId: docData.id
    };
};

export const reactivateUser = async (docId) => {
    const docRef = doc(db, TABLES.WAITLIST, docId);
    const newPosition = await getWaitlistPosition();
    
    await updateDoc(docRef, {
      status: 'active',
      reactivatedAt: new Date().toISOString(),
      position: newPosition,
      effectivePosition: newPosition,
      positionBonus: 0,
      totalReferrals: 0
    });
    
    return newPosition;
};

// Generate a unique, URL-friendly referral code
const generateReferralCode = (email) => {
    const timestamp = Date.now().toString();
    const uniqueString = `${email}${timestamp}${process.env.REFERRAL_SECRET}`;
    return crypto
        .createHash('md5')
        .update(uniqueString)
        .digest('hex')
        .substring(0, 8);
};

// Get current position in waitlist - simplified to avoid composite index
export const getWaitlistPosition = async () => {
    const waitlistRef = collection(db, TABLES.WAITLIST);
    const q = query(waitlistRef, where('status', '==', 'active'));
    const snapshot = await getCountFromServer(q);
    return snapshot.data().count + 1;
};

// Process referral and update referrer's position
export const processReferral = async (referralCode) => {
    if (!referralCode) return null;
    
    const waitlistRef = collection(db, TABLES.WAITLIST);
    const referrerQuery = query(
      waitlistRef, 
      where('referralCode', '==', referralCode),
      where('status', '==', 'active')
    );
    const referrerSnapshot = await getDocs(referrerQuery);
    
    if (!referrerSnapshot.empty) {
      const referrerDoc = referrerSnapshot.docs[0];
      const referrerData = referrerDoc.data();
      
      await updateDoc(referrerDoc.ref, {
        positionBonus: increment(5),
        totalReferrals: increment(1)
      });

      return {
        referrerId: referrerDoc.id,
        referrerEmail: referrerData.email
      };
    }
    
    return null;
};

// Add new user to waitlist
export const addToWaitlist = async (email, referralCode = null) => {
    const waitlistRef = collection(db, TABLES.WAITLIST);
    const newReferralCode = generateReferralCode(email);
    const referrerInfo = await processReferral(referralCode);
    
    const position = await getWaitlistPosition();
    
    const docData = {
        email: email.toLowerCase(),
        timestamp: new Date().toISOString(),
        status: 'active',
        referralCode: newReferralCode,
        position: position,
        positionBonus: 0,
        effectivePosition: position,
        referredBy: referrerInfo?.referrerId || null,
        referredByEmail: referrerInfo?.referrerEmail || null,
        totalReferrals: 0,
        environment: isDevelopment() ? 'development' : 'production'
    };

    const newDoc = await addDoc(waitlistRef, docData);
    return {
        ...docData,
        id: newDoc.id
    };
};

export const getEffectivePosition = async (position, positionBonus) => {
    return Math.max(1, position - positionBonus);
};