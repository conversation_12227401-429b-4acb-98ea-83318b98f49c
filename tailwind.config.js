/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      spacing: {
        // Percentage-based spacing
        '1/10': '10%',
        '1/20': '5%',
        // CSS variable-based spacing
        'xs': 'var(--space-xs)',
        'sm': 'var(--space-sm)',
        'md': 'var(--space-md)',
        'lg': 'var(--space-lg)',
        'xl': 'var(--space-xl)',
        '2xl': 'var(--space-2xl)',
        '3xl': 'var(--space-3xl)',
      },
      maxWidth: {
        'content': '1400px',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in',
        'slide-up': 'slideUp 0.5s ease-out',
        'slide-down': 'slideDown 0.5s ease-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
      colors: {
        // Theme colors
        background: "var(--background)",
        foreground: "var(--foreground)",
        'text-primary': "var(--text-primary)",
        'text-secondary': "var(--text-secondary)",

        // Primary colors
        'primary-green': "var(--primary-green)",
        'primary-green-light': "var(--primary-green-light)",
        'primary-green-dark': "var(--primary-green-dark)",

        // Neutral colors
        'cream': "var(--cream)",
        'cream-light': "var(--cream-light)",
        'cream-dark': "var(--cream-dark)",

        // UI colors
        'card-bg': "var(--card-bg)",
        'card-border': "var(--card-border)",
        'input-bg': "var(--input-bg)",
        'input-border': "var(--input-border)",
        'input-text': "var(--input-text)",

        // Feedback colors
        'success': "var(--success)",
        'warning': "var(--warning)",
        'error': "var(--error)",
        'info': "var(--info)",
      },
      backgroundImage: {
        'app-pattern': "url('/background.svg')",
        'rectangle-26': "url('/background.svg')",
      },
      backdropBlur: {
        '80': '80px',
      },
      fontFamily: {
        libre: ['var(--font-libre-baskerville)'],
        figtree: ['var(--font-figtree)'],
        slackey: ['var(--font-slackey)'],
        inter: ['var(--font-inter)'],
      },
      fontSize: {
        // Fluid typography settings
        'fluid-sm': 'clamp(0.8rem, 1.5vw, 1rem)',
        'fluid-base': 'clamp(1rem, 2vw, 1.2rem)',
        'fluid-lg': 'clamp(1.2rem, 3vw, 2rem)',
        'fluid-xl': 'clamp(1.5rem, 4vw, 3rem)',
      },
      borderRadius: {
        'sm': 'var(--radius-sm)',
        'md': 'var(--radius-md)',
        'lg': 'var(--radius-lg)',
        'full': 'var(--radius-full)',
      },
      boxShadow: {
        'sm': 'var(--shadow-sm)',
        'md': 'var(--shadow-md)',
        'lg': 'var(--shadow-lg)',
      },
      transitionProperty: {
        'height': 'height',
        'spacing': 'margin, padding',
      },
      transitionDuration: {
        'fast': '150ms',
        'normal': '300ms',
        'slow': '500ms',
      },
      transitionTimingFunction: {
        'default': 'cubic-bezier(0.4, 0, 0.2, 1)',
        'in': 'cubic-bezier(0.4, 0, 1, 1)',
        'out': 'cubic-bezier(0, 0, 0.2, 1)',
        'in-out': 'cubic-bezier(0.4, 0, 0.2, 1)',
      },
      zIndex: {
        'base': 'var(--z-base)',
        'dropdown': 'var(--z-dropdown)',
        'sticky': 'var(--z-sticky)',
        'fixed': 'var(--z-fixed)',
        'modal': 'var(--z-modal)',
        'popover': 'var(--z-popover)',
        'tooltip': 'var(--z-tooltip)',
      },
    },
  },
  plugins: [],
};
